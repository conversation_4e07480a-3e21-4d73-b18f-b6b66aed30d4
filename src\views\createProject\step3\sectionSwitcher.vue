<script setup lang="ts">
import { ref, watch } from "vue";
import type { simpleSectionInfo } from "@/utils/type";
import { ArrowLeftBold, ArrowRightBold } from "@element-plus/icons-vue";
import ModeTypeSwitcher from "@/views/createProject/step3/modeTypeSwitcher.vue";

const emit = defineEmits(["changeSection"]);
const props = defineProps({
  curSectionId: {
    type: Number,
    required: true,
  },
  sectionList: {
    type: Array as () => simpleSectionInfo[],
    required: true,
  },
});
const wrapperRef = ref();
const secList = ref<simpleSectionInfo[]>([]);
const curSecId = ref();
// const curSection = ref<simpleSectionInfo>();
watch(
  () => props,
  (newValue, oldValue) => {
    secList.value = newValue.sectionList;
    curSecId.value = newValue.curSectionId;
  },
  { deep: true, immediate: true }
);
const handleWheel = (e: WheelEvent) => {
  const scrollTime = e.deltaY / 100; // 1次
  const speed = 274 + 5; // 1条 + margin
  wrapperRef.value.scrollLeft += scrollTime * speed;
  const scrollLeft = wrapperRef.value.scrollLeft;
  const scrollRight =
    wrapperRef.value.scrollWidth -
    wrapperRef.value.scrollLeft -
    wrapperRef.value.clientWidth;
  // document.getElementById('leftIcon').style.opacity = scrollLeft <= 0 ? '0' : '100';
  // document.getElementById('rightIcon').style.opacity = scrollRight <= 0 ? '0' : '100';
  // console.log(wrapperRef.value.scrollWidth);
  // console.log('scrollLeft: ',wrapperRef.value.scrollLeft);
  // console.log('scrollRight: ',scrollRight);
};
const handleChangeSection = (newSectionId: number) => {
  // console.log(22222)
  emit("changeSection", newSectionId);
};

// 获取当前章节在列表中的索引
const getCurrentSectionIndex = () => {
  return secList.value.findIndex(
    (sec: simpleSectionInfo) => sec.sectionId === curSecId.value
  );
};

// 切换到上一章节
const handlePrevSection = () => {
  const currentIndex = getCurrentSectionIndex();
  if (currentIndex > 0) {
    const prevSection = secList.value[currentIndex - 1];
    handleChangeSection(prevSection.sectionId);
  }
};

// 切换到下一章节
const handleNextSection = () => {
  const currentIndex = getCurrentSectionIndex();
  if (currentIndex < secList.value.length - 1) {
    const nextSection = secList.value[currentIndex + 1];
    handleChangeSection(nextSection.sectionId);
  }
};

// 判断是否可以切换到上一章节
const canGoPrev = () => {
  const currentIndex = getCurrentSectionIndex();
  return currentIndex > 0;
};

// 判断是否可以切换到下一章节
const canGoNext = () => {
  const currentIndex = getCurrentSectionIndex();
  return currentIndex < secList.value.length - 1;
};
</script>

<template>
  <div class="outer-wrapper">
    <!-- 左切换按钮 -->
    <span
      class="nav-button left-button"
      :class="{ disabled: !canGoPrev() }"
      @click="handlePrevSection"
      title="上一章节"
    >
      <el-icon>
        <ArrowLeftBold />
      </el-icon>
    </span>

    <div class="switch-wrapper" ref="wrapperRef" @wheel.prevent="handleWheel">
      <span
        :title="sec.sectionName"
        @click="handleChangeSection(sec.sectionId)"
        class="section "
        :class="curSecId == sec.sectionId ? 'isFocuse' : ''"
        v-for="(sec, idx) in secList"
        :key="sec.sectionId"
        v-html="`第${idx + 1}节 ${sec.sectionName}`"
      >
      </span>
    </div>

    <!-- 右切换按钮 -->
    <span
      class="nav-button right-button"
      :class="{ disabled: !canGoNext() }"
      @click="handleNextSection"
      title="下一章节"
    >
      <el-icon>
        <ArrowRightBold />
      </el-icon>
    </span>
  </div>
</template>

<style scoped>
.outer-wrapper {
  display: flex;
  align-items: center;
  flex-direction: row;
  margin-bottom: 20px;
  width: 100%;
  gap: 10px;

  .nav-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--color-primary);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;

    &:hover:not(.disabled) {
      background-color: var(--color-primary-dark, #409eff);
      transform: scale(1.1);
    }

    &.disabled {
      background-color: #c0c4cc;
      cursor: not-allowed;
      opacity: 0.6;
    }

    .el-icon {
      font-size: 16px;
    }
  }

  .icon {
    /*position: sticky;*/
    /*position: fixed;*/
    position: absolute;
    z-index: 10;
    background-color: var(--color-primary-transparent);
    display: flex;
    height: 20px;
    width: 20px;
    border-radius: 10px;
    justify-content: center;
    align-items: center;
    color: white;
  }

  .switch-wrapper {
    display: flex;
    flex-direction: row;
    width: 100%;
    overflow-x: hidden;

    .section {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 5px;
      font-size: 14px;
      border: 1px solid var(--color-primary);
      color: var(--color-primary);
      min-width: 260px;
      /* width: 260px; */
      height: 35px;
      display: flex;
      align-items: center;
      padding: 0 10px;

      &.isFocuse,
      &:hover {
        background-color: var(--color-primary);
        cursor: pointer;
        color: white;
      }
    }
  }
}
</style>
