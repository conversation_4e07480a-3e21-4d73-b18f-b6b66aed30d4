<script setup lang="ts">
import { ref, watch } from "vue";
import type { simpleSectionInfo } from "@/utils/type";
import { ArrowLeftBold, ArrowRightBold } from "@element-plus/icons-vue";
import ModeTypeSwitcher from "@/views/createProject/step3/modeTypeSwitcher.vue";

const emit = defineEmits(["changeSection"]);
const props = defineProps({
  curSectionId: {
    type: Number,
    required: true,
  },
  sectionList: {
    type: Array as () => simpleSectionInfo[],
    required: true,
  },
});
const wrapperRef = ref();
const secList = ref<simpleSectionInfo[]>([]);
const curSecId = ref();
// const curSection = ref<simpleSectionInfo>();
watch(
  () => props,
  (newValue, oldValue) => {
    secList.value = newValue.sectionList;
    curSecId.value = newValue.curSectionId;
  },
  { deep: true, immediate: true }
);
const handleWheel = (e: WheelEvent) => {
  const scrollTime = e.deltaY / 100; // 1次
  const speed = 274 + 5; // 1条 + margin
  wrapperRef.value.scrollLeft += scrollTime * speed;
  const scrollLeft = wrapperRef.value.scrollLeft;
  const scrollRight =
    wrapperRef.value.scrollWidth -
    wrapperRef.value.scrollLeft -
    wrapperRef.value.clientWidth;
  // document.getElementById('leftIcon').style.opacity = scrollLeft <= 0 ? '0' : '100';
  // document.getElementById('rightIcon').style.opacity = scrollRight <= 0 ? '0' : '100';
  // console.log(wrapperRef.value.scrollWidth);
  // console.log('scrollLeft: ',wrapperRef.value.scrollLeft);
  // console.log('scrollRight: ',scrollRight);
};
const handleChangeSection = (newSectionId: number, skipScroll = false) => {
  // 如果不跳过滚动，则应用智能滚动逻辑
  if (!skipScroll) {
    const targetIndex = secList.value.findIndex(
      (sec: simpleSectionInfo) => sec.sectionId === newSectionId
    );
    if (targetIndex !== -1) {
      scrollToSection(targetIndex);
    }
  }

  emit("changeSection", newSectionId);
};

// 获取当前章节在列表中的索引
const getCurrentSectionIndex = () => {
  return secList.value.findIndex(
    (sec: simpleSectionInfo) => sec.sectionId === curSecId.value
  );
};

// 缓动函数 - easeInOutCubic
const easeInOutCubic = (t: number): number => {
  return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
};

// 平滑滚动动画
const smoothScrollTo = (targetScrollLeft: number, duration = 300) => {
  if (!wrapperRef.value) return;

  const startScrollLeft = wrapperRef.value.scrollLeft;
  const distance = targetScrollLeft - startScrollLeft;

  // 如果距离很小，直接设置
  if (Math.abs(distance) < 1) {
    wrapperRef.value.scrollLeft = targetScrollLeft;
    return;
  }

  const startTime = performance.now();

  const animateScroll = (currentTime: number) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // 应用缓动函数
    const easedProgress = easeInOutCubic(progress);
    const currentScrollLeft = startScrollLeft + distance * easedProgress;

    wrapperRef.value!.scrollLeft = currentScrollLeft;

    if (progress < 1) {
      requestAnimationFrame(animateScroll);
    }
  };

  requestAnimationFrame(animateScroll);
};

// 智能滚动到指定章节位置
const scrollToSection = (targetIndex: number) => {
  if (!wrapperRef.value) return;

  const sectionWidth = 255 + 5; // 章节宽度 + margin
  const containerWidth = wrapperRef.value.clientWidth;
  const maxVisibleSections = Math.floor(containerWidth / sectionWidth);

  let targetScrollLeft = wrapperRef.value.scrollLeft; // 默认保持当前位置

  // 如果目标章节索引大于等于可视区域能显示的章节数，需要滚动
  if (targetIndex >= maxVisibleSections) {
    // 计算需要滚动的位置，让目标章节显示在可视区域的最后一个位置
    targetScrollLeft = (targetIndex - maxVisibleSections + 1) * sectionWidth;
  } else if (
    targetIndex < Math.floor(wrapperRef.value.scrollLeft / sectionWidth)
  ) {
    // 如果目标章节在当前可视区域的左侧，滚动到该章节
    targetScrollLeft = targetIndex * sectionWidth;
  }

  // 使用平滑滚动动画
  smoothScrollTo(targetScrollLeft);
};

// 切换到上一章节
const handlePrevSection = () => {
  const currentIndex = getCurrentSectionIndex();
  if (currentIndex > 0) {
    const prevSection = secList.value[currentIndex - 1];
    const targetIndex = currentIndex - 1;

    // 先滚动到目标位置
    scrollToSection(targetIndex);

    // 然后切换章节，跳过内部滚动逻辑避免重复
    handleChangeSection(prevSection.sectionId, true);
  }
};

// 切换到下一章节
const handleNextSection = () => {
  const currentIndex = getCurrentSectionIndex();
  if (currentIndex < secList.value.length - 1) {
    const nextSection = secList.value[currentIndex + 1];
    const targetIndex = currentIndex + 1;

    // 从第3个章节开始（索引2），切换到下一章节时需要滚动
    if (targetIndex >= 2) {
      scrollToSection(targetIndex);
    }

    // 然后切换章节，跳过内部滚动逻辑避免重复
    handleChangeSection(nextSection.sectionId, true);
  }
};

// 判断是否可以切换到上一章节
const canGoPrev = () => {
  const currentIndex = getCurrentSectionIndex();
  return currentIndex > 0;
};

// 判断是否可以切换到下一章节
const canGoNext = () => {
  const currentIndex = getCurrentSectionIndex();
  return currentIndex < secList.value.length - 1;
};
</script>

<template>
  <div class="outer-wrapper">
    <!-- 左切换按钮 -->
    <span
      class="nav-button left-button"
      :class="{ disabled: !canGoPrev() }"
      @click="handlePrevSection"
      title="上一章节"
    >
      <el-icon>
        <ArrowLeftBold />
      </el-icon>
    </span>

    <div class="switch-wrapper" ref="wrapperRef" @wheel.prevent="handleWheel">
      <span
        :title="sec.sectionName"
        @click="handleChangeSection(sec.sectionId)"
        class="section ellipsis-text-inline"
        :class="curSecId == sec.sectionId ? 'isFocuse' : ''"
        v-for="(sec, idx) in secList"
        :key="sec.sectionId"
        v-html="`第${idx + 1}节 ${sec.sectionName}`"
      >
      </span>
    </div>

    <!-- 右切换按钮 -->
    <span
      class="nav-button right-button"
      :class="{ disabled: !canGoNext() }"
      @click="handleNextSection"
      title="下一章节"
    >
      <el-icon>
        <ArrowRightBold />
      </el-icon>
    </span>
  </div>
</template>

<style scoped>
.outer-wrapper {
  display: flex;
  align-items: center;
  flex-direction: row;
  margin-bottom: 20px;
  width: 100%;
  gap: 10px;

  .nav-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--color-primary);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;

    &:hover:not(.disabled) {
      background-color: var(--color-primary-dark, #409eff);
      transform: scale(1.1);
    }

    &.disabled {
      background-color: #c0c4cc;
      cursor: not-allowed;
      opacity: 0.6;
    }

    .el-icon {
      font-size: 16px;
    }
  }

  .icon {
    /*position: sticky;*/
    /*position: fixed;*/
    position: absolute;
    z-index: 10;
    background-color: var(--color-primary-transparent);
    display: flex;
    height: 20px;
    width: 20px;
    border-radius: 10px;
    justify-content: center;
    align-items: center;
    color: white;
  }

  .switch-wrapper {
    display: flex;
    flex-direction: row;
    width: 100%;
    overflow-x: hidden;

    .section {
      margin-right: 5px;
      font-size: 14px;
      border: 1px solid var(--color-primary);
      color: var(--color-primary);
      min-width: 258px;
      max-width: 258px;
      height: 35px;
      display: flex;
      align-items: center;
      padding: 0 10px;
      &:hover {
        cursor: pointer;
      }
      &.isFocuse {
        background-color: var(--color-primary);
        cursor: pointer;
        color: white;
      }
    }
  }
}
</style>
